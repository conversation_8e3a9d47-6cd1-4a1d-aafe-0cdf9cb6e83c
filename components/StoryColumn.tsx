"use client";

import { <PERSON><PERSON>, <PERSON> } from "@/lib/types";
import StoryCard from "@/components/StoryCard";
import AddStoryButton from "@/components/AddStoryButton";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle } from "lucide-react";

interface StoryColumnProps {
  milestone: Milestone;
  milestones: Milestone[];
  onAddStory: () => void;
  onUpdateStory: (storyId: string, updates: Partial<Story>) => void;
  onDeleteStory: (storyId: string) => void;
}

// Placeholder story card component for empty columns
function PlaceholderStoryCard({ milestoneColor, onAddStory }: { milestoneColor: string; onAddStory: () => void }) {
  // Get border color based on milestone color (same as StoryCard)
  const getBorderColor = () => {
    const colorMap: Record<string, string> = {
      red: "border-red-200 dark:border-red-800",
      pink: "border-pink-200 dark:border-pink-800",
      rose: "border-rose-200 dark:border-rose-800",
      orange: "border-orange-200 dark:border-orange-800",
      amber: "border-amber-200 dark:border-amber-800",
      yellow: "border-yellow-200 dark:border-yellow-800",
      lime: "border-lime-200 dark:border-lime-800",
      green: "border-green-200 dark:border-green-800",
      emerald: "border-emerald-200 dark:border-emerald-800",
      teal: "border-teal-200 dark:border-teal-800",
      cyan: "border-cyan-200 dark:border-cyan-800",
      sky: "border-sky-200 dark:border-sky-800",
      blue: "border-blue-200 dark:border-blue-800",
      indigo: "border-indigo-200 dark:border-indigo-800",
      violet: "border-violet-200 dark:border-violet-800",
      purple: "border-purple-200 dark:border-purple-800",
      fuchsia: "border-fuchsia-200 dark:border-fuchsia-800",
      gray: "border-gray-200 dark:border-gray-700",
    };
    
    return colorMap[milestoneColor] || "border-gray-200 dark:border-gray-700";
  };

  // Get hover background color based on milestone color
  const getHoverColor = () => {
    const colorMap: Record<string, string> = {
      red: "hover:bg-red-50 dark:hover:bg-red-900/10",
      pink: "hover:bg-pink-50 dark:hover:bg-pink-900/10",
      rose: "hover:bg-rose-50 dark:hover:bg-rose-900/10",
      orange: "hover:bg-orange-50 dark:hover:bg-orange-900/10",
      amber: "hover:bg-amber-50 dark:hover:bg-amber-900/10",
      yellow: "hover:bg-yellow-50 dark:hover:bg-yellow-900/10",
      lime: "hover:bg-lime-50 dark:hover:bg-lime-900/10",
      green: "hover:bg-green-50 dark:hover:bg-green-900/10",
      emerald: "hover:bg-emerald-50 dark:hover:bg-emerald-900/10",
      teal: "hover:bg-teal-50 dark:hover:bg-teal-900/10",
      cyan: "hover:bg-cyan-50 dark:hover:bg-cyan-900/10",
      sky: "hover:bg-sky-50 dark:hover:bg-sky-900/10",
      blue: "hover:bg-blue-50 dark:hover:bg-blue-900/10",
      indigo: "hover:bg-indigo-50 dark:hover:bg-indigo-900/10",
      violet: "hover:bg-violet-50 dark:hover:bg-violet-900/10",
      purple: "hover:bg-purple-50 dark:hover:bg-purple-900/10",
      fuchsia: "hover:bg-fuchsia-50 dark:hover:bg-fuchsia-900/10",
      gray: "hover:bg-gray-50 dark:hover:bg-gray-800/50",
    };
    
    return colorMap[milestoneColor] || "hover:bg-gray-50 dark:hover:bg-gray-800/50";
  };

  return (
    <Card 
      className={`rounded-2xl border-dashed border-2 ${getBorderColor()} bg-white/30 dark:bg-[oklch(0.2466_0.0316_260.4/0.7)] ${getHoverColor()} transition-all duration-200 cursor-pointer hover:scale-[1.02]`}
      onClick={onAddStory}
    >
      <CardContent className="px-2 pt-4 pb-4 sm:px-3 sm:pt-6 sm:pb-6 flex flex-col items-center justify-center min-h-[120px]">
        <PlusCircle className="h-8 w-8 text-gray-400 dark:text-gray-500 mb-2" />
        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Add Story</span>
      </CardContent>
    </Card>
  );
}

export default function StoryColumn({
  milestone,
  milestones,
  onAddStory,
  onUpdateStory,
  onDeleteStory,
}: StoryColumnProps) {
  return (
    <div className="w-[280px] sm:w-[320px] md:w-[360px] lg:w-[400px] flex flex-col rounded-2xl p-3">
      {/* Story cards container */}
      <div className="flex flex-col gap-3">
        {milestone.stories.length === 0 ? (
          <PlaceholderStoryCard milestoneColor={milestone.color} onAddStory={onAddStory} />
        ) : (
          milestone.stories.map((story) => (
            <StoryCard
              key={story.id}
              story={story}
              milestones={milestones}
              milestoneColor={milestone.color}
              onUpdate={(updates: Partial<Story>) => onUpdateStory(story.id, updates)}
              onDelete={() => onDeleteStory(story.id)}
            />
          ))
        )}
      </div>
      
      {/* Add Story Button positioned outside the colored background - only show when there are existing stories */}
      {milestone.stories.length > 0 && (
        <AddStoryButton onAddStory={onAddStory} milestoneColor={milestone.color} />
      )}
    </div>
  );
}
